// 杭高天文社解析模板 - 简洁版
// 快速开始：修改标题和周次，然后添加问题内容

// ==================== 配置区域（可修改） ====================
#let week_info = "第X周附加题答案与解析"  // 修改周次信息
#let doc_title = "文档标题"              // 修改文档标题

// ==================== 样式设置（一般不需修改） ====================
#set page(
  paper: "a4",
  margin: (top: 2.5cm, bottom: 2cm, left: 2cm, right: 2cm),
  header: [
    #set text(size: 9pt, fill: gray)
    #grid(
      columns: (1fr, 1fr),
      align: (left, right),
      [杭高天文社],
      [#text(size: 10pt)[杭高天文社 2025 暑期每周天文 | #week_info]],
    )
    #line(length: 100%, stroke: 0.5pt + gray)
  ],
  footer: [
    #set text(size: 8pt, fill: gray)
    #line(length: 100%, stroke: 0.5pt + gray)
    #v(0.2em)
    #grid(
      columns: (1fr, 1fr),
      align: (left, right),
      [©Hangao Observatory],
      [内部资料，请勿外传]
    )
  ]
)

#set text(font: "STSong", size: 11pt, lang: "zh")
#set par(justify: true, leading: 0.65em, first-line-indent: 2em)
#set heading(numbering: none)

// 问题函数
#let problem(number, title) = [
  #v(1em)
  #text(size: 12pt, weight: "bold")[#number #title]
  #v(0.5em)
]

// 解析框函数
#let solution(content) = [
  #v(0.5em)
  #block(
    fill: rgb("#fff5f5"),
    width: 100%,
    inset: 1em,
    radius: 4pt,
    stroke: 1pt + rgb("#feb2b2")
  )[
    #text(weight: "bold", fill: rgb("#c53030"))[【解析】] #content
    #v(0.5em)
  ]
]

// 行间公式样式
#show math.equation.where(block: true): it => [
  #v(0.3em)
  #align(center)[#it]
  #v(0.3em)
]

// ==================== 文档内容 ====================
#align(center)[
  #text(size: 16pt, weight: "bold")[#doc_title]
  #v(1em)
]

// ==================== 在下方添加问题 ====================

#problem("1-1", "问题标题")

// 问题内容...

#solution[
解析内容...
]

#problem("1-2", "问题标题")

// 问题内容...

#solution[
解析内容...
]

// 继续添加更多问题...
