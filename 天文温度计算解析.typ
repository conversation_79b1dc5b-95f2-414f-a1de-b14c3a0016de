#set page(
  paper: "a4",
  margin: (top: 2.5cm, bottom: 2.5cm, left: 2cm, right: 2cm),
  header: align(center)[
    #text(size: 10pt, fill: gray)[天文温度计算解析]
    #line(length: 100%, stroke: 0.5pt + gray)
  ],
  footer: align(center)[
    #line(length: 100%, stroke: 0.5pt + gray)
    #context text(size: 10pt, fill: gray)[第 #counter(page).display() 页]
  ]
)

#set text(font: "STSong", size: 12pt, lang: "zh")
#set par(justify: true, leading: 0.8em, first-line-indent: 2em)
#set heading(numbering: "1.")
#set math.equation(numbering: "(1)")

// 标题样式
#show heading.where(level: 1): it => [
  #v(1.5em)
  #block(
    fill: rgb("#f0f8ff"),
    width: 100%,
    inset: 1em,
    radius: 8pt,
    stroke: 1pt + rgb("#4682b4")
  )[
    #text(size: 16pt, weight: "bold", fill: rgb("#2c5282"))[#it.body]
  ]
  #v(0.8em)
]

#show heading.where(level: 2): it => [
  #v(1em)
  #text(size: 14pt, weight: "bold", fill: rgb("#2d3748"))[
    #box(
      fill: rgb("#e2e8f0"),
      inset: (x: 0.8em, y: 0.4em),
      radius: 4pt
    )[#it.body]
  ]
  #v(0.5em)
]

// 数学公式样式
#show math.equation: it => [
  #v(0.5em)
  #block(
    fill: rgb("#fafafa"),
    width: 100%,
    inset: 1em,
    radius: 6pt,
    stroke: 0.5pt + rgb("#e2e8f0")
  )[
    #align(center)[#it]
  ]
  #v(0.5em)
]

// 主标题
#align(center)[
  #block(
    fill: gradient.linear(rgb("#667eea"), rgb("#764ba2")),
    width: 100%,
    inset: 2em,
    radius: 12pt
  )[
    #text(size: 24pt, weight: "bold", fill: white)[天文温度计算解析]
    #v(0.5em)
    #text(size: 14pt, fill: rgb("#f7fafc"))[基于黑体辐射理论的太阳与地球温度估算]
  ]
]

#v(2em)

= 太阳温度的估算

== 太阳总辐射功率

视太阳为#text(weight: "bold", fill: rgb("#e53e3e"))[黑体]，表面每单位面积的辐射功率为

$ j = sigma T^4 $

其中 $sigma = 5.67 times 10^(-8) space "W/m"^2 "K"^4$，$T$ 为太阳表面温度。

整个太阳球面的面积为 $4 pi R^2$，所以太阳总功率

$ P_"tot" = j dot 4 pi R^2 = 4 pi R^2 sigma T^4 $

== 地球处的辐射通量

太阳以各向同性向外辐射，距离太阳中心 $D$ 处的单位面积收到的功率（即#text(weight: "bold", fill: rgb("#38a169"))[太阳常数]）为

$ F = (P_"tot")/(4 pi D^2) = (4 pi R^2 sigma T^4)/(4 pi D^2) = sigma T^4 (R^2)/(D^2) $

#block(
  fill: rgb("#fff5f5"),
  width: 100%,
  inset: 1em,
  radius: 6pt,
  stroke: 1pt + rgb("#feb2b2")
)[
  #text(weight: "bold")[已知参数：]
  - 太阳常数：$F = 1367 space "W/m"^2$
  - 太阳半径：$R = 70"万千米" = 7.0 times 10^8 space "m"$
  - 日地距离：$D = 1.5 times 10^8 space "km" = 1.5 times 10^11 space "m"$
]

== 解出太阳温度

$ sigma T^4 = F dot (D^2)/(R^2) $

$ arrow.double.r T = ((F D^2)/(sigma R^2))^(1/4) $

将数值代入：

$ T = ((1367 times (1.5 times 10^11)^2)/(5.67 times 10^(-8) times (7.0 times 10^8)^2))^(1/4) approx 5.77 times 10^3 space "K" $

#block(
  fill: rgb("#f0fff4"),
  width: 100%,
  inset: 1em,
  radius: 6pt,
  stroke: 1pt + rgb("#9ae6b4")
)[
  #align(center)[
    #text(size: 14pt, weight: "bold", fill: rgb("#2f855a"))[
      计算结果：太阳表面温度 ≈ 5770 K
    ]
  ]
]

#pagebreak()

= 地球温度的估算

== 地球吸收的平均通量

地球接收太阳辐射时，只能截获投影面积为 $pi r^2$（$r$ 为地球半径）的光束，但要在整个球面 $4 pi r^2$ 上重新辐射达到平衡。因此平均到单位面积的吸收通量为

$ F_"avg" = (F dot pi r^2)/(4 pi r^2) = F/4 $

== 平衡条件

当地球吸收的功率等于自身黑体辐射功率时，有

$ sigma T_e^4 = F/4 $

$ arrow.double.r T_e = (F/(4 sigma))^(1/4) $

== 解出地球温度

$ T_e = (1367/(4 times 5.67 times 10^(-8)))^(1/4) approx 2.79 times 10^2 space "K" $

#block(
  fill: rgb("#f0fff4"),
  width: 100%,
  inset: 1em,
  radius: 6pt,
  stroke: 1pt + rgb("#9ae6b4")
)[
  #align(center)[
    #text(size: 14pt, weight: "bold", fill: rgb("#2f855a"))[
      计算结果：地球平衡温度 ≈ 279 K
    ]
  ]
]

#v(2em)

== 最终结果

#block(
  fill: gradient.linear(rgb("#4299e1"), rgb("#3182ce")),
  width: 100%,
  inset: 2em,
  radius: 10pt
)[
  #align(center)[
    #text(size: 18pt, weight: "bold", fill: white)[计算总结]
  ]
  #v(1em)
  #grid(
    columns: 2,
    gutter: 2em,
    [
      #block(
        fill: white,
        width: 100%,
        inset: 1em,
        radius: 8pt
      )[
        #align(center)[
          #text(size: 16pt, weight: "bold", fill: rgb("#e53e3e"))[☀️ 太阳温度]
          #v(0.5em)
          #text(size: 20pt, weight: "bold")[5770 K]
          #v(0.3em)
          #text(size: 12pt, fill: gray)[约 5497°C]
        ]
      ]
    ],
    [
      #block(
        fill: white,
        width: 100%,
        inset: 1em,
        radius: 8pt
      )[
        #align(center)[
          #text(size: 16pt, weight: "bold", fill: rgb("#3182ce"))[🌍 地球温度]
          #v(0.5em)
          #text(size: 20pt, weight: "bold")[279 K]
          #v(0.3em)
          #text(size: 12pt, fill: gray)[约 6°C]
        ]
      ]
    ]
  )
]

#v(1em)

#block(
  fill: rgb("#fffaf0"),
  width: 100%,
  inset: 1em,
  radius: 6pt,
  stroke: 1pt + rgb("#fbd38d")
)[
  #text(weight: "bold", fill: rgb("#c05621"))[💡 说明：]

  本计算基于黑体辐射理论，假设太阳和地球都是理想黑体。实际情况中，地球表面平均温度约为15°C（288K），比计算值高出约9K，这主要是由于大气层的温室效应所致。
]