#set page(paper: "a4", margin: 2cm)
#set text(font: "STSong", size: 12pt, lang: "zh")
#set heading(numbering: "1.")
#set math.equation(numbering: "(1)")

#align(center)[
  #text(size: 18pt, weight: "bold")[天文温度计算解析]
]

#v(1em)

= 太阳温度的估算

== 太阳总辐射功率

视太阳为*黑体*，表面每单位面积的辐射功率为

$ j = sigma T^4 $

其中 $sigma = 5.67 times 10^(-8) space "W/m"^2 "K"^4$，$T$ 为太阳表面温度。

整个太阳球面的面积为 $4 pi R^2$，所以太阳总功率

$ P_"tot" = j dot 4 pi R^2 = 4 pi R^2 sigma T^4 $

== 地球处的辐射通量

太阳以各向同性向外辐射，距离太阳中心 $D$ 处的单位面积收到的功率（即*太阳常数*）为

$ F = (P_"tot")/(4 pi D^2) = (4 pi R^2 sigma T^4)/(4 pi D^2) = sigma T^4 (R^2)/(D^2) $

已知 $F = 1367 space "W/m"^2$，$R = 70"万千米" = 7.0 times 10^8 space "m"$，$D = 1.5 times 10^8 space "km" = 1.5 times 10^11 space "m"$。

== 解出太阳温度

$ sigma T^4 = F dot (D^2)/(R^2) $

$ arrow.double.r T = ((F D^2)/(sigma R^2))^(1/4) $

将数值代入：

$ T = ((1367 times (1.5 times 10^11)^2)/(5.67 times 10^(-8) times (7.0 times 10^8)^2))^(1/4) approx 5.77 times 10^3 space "K" $

#line(length: 100%)

= 地球温度的估算

== 地球吸收的平均通量

地球接收太阳辐射时，只能截获投影面积为 $pi r^2$（$r$ 为地球半径）的光束，但要在整个球面 $4 pi r^2$ 上重新辐射达到平衡。因此平均到单位面积的吸收通量为

$ F_"avg" = (F dot pi r^2)/(4 pi r^2) = F/4 $

== 平衡条件

当地球吸收的功率等于自身黑体辐射功率时，有

$ sigma T_e^4 = F/4 $

$ arrow.double.r T_e = (F/(4 sigma))^(1/4) $

== 解出地球温度

$ T_e = (1367/(4 times 5.67 times 10^(-8)))^(1/4) approx 2.79 times 10^2 space "K" $

#line(length: 100%)

== 最终结果

最终结果（取整数并附单位）：

太阳温度 $T approx 5770 space "K"$；地球温度 $T_e approx 279 space "K"$。