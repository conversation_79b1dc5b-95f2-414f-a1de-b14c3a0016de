// 杭高天文社解析模板
// 使用方法：复制此模板，修改标题、内容等信息

// ==================== 文档配置 ====================
#set page(
  paper: "a4",
  margin: (top: 2.5cm, bottom: 2cm, left: 2cm, right: 2cm),
  header: [
    #set text(size: 9pt, fill: gray)
    #grid(
      columns: (1fr, 1fr),
      align: (left, right),
      [杭高天文社],
      [#text(size: 10pt)[杭高天文社 2025 暑期每周天文 | 第X周附加题答案与解析]],
    )
    #line(length: 100%, stroke: 0.5pt + gray)
  ],
  footer: [
    #set text(size: 8pt, fill: gray)
    #line(length: 100%, stroke: 0.5pt + gray)
    #v(0.2em)
    #grid(
      columns: (1fr, 1fr),
      align: (left, right),
      [©Hangao Observatory],
      [内部资料，请勿外传]
    )
  ]
)

#set text(font: "STSong", size: 11pt, lang: "zh")
#set par(justify: true, leading: 0.65em, first-line-indent: 2em)
#set heading(numbering: none)

// ==================== 自定义函数 ====================

// 问题标题函数
#let problem(number, title) = [
  #v(1em)
  #text(size: 12pt, weight: "bold")[#number #title]
  #v(0.5em)
]

// 解析框函数
#let solution(content) = [
  #v(0.5em)
  #block(
    fill: rgb("#fff5f5"),
    width: 100%,
    inset: 1em,
    radius: 4pt,
    stroke: 1pt + rgb("#feb2b2")
  )[
    #text(weight: "bold", fill: rgb("#c53030"))[【解析】] #content
    #v(0.5em)
  ]
]

// 数学公式样式 - 只对行间公式应用样式
#show math.equation.where(block: true): it => [
  #v(0.3em)
  #align(center)[#it]
  #v(0.3em)
]

// ==================== 文档标题 ====================
#align(center)[
  #text(size: 16pt, weight: "bold")[
    [文档标题] // 在此处修改文档标题
  ]
  #v(1em)
]

// ==================== 内容区域 ====================

#problem("X-1", "问题标题1") // 修改问题编号和标题

// 在此处添加问题内容
// 使用行内公式：$x = y$
// 使用行间公式：$ x = y $

#solution[
在此处添加解析内容。可以包含：
- 解题思路
- 关键概念解释
- 物理意义说明
- 注意事项等
]

#problem("X-2", "问题标题2") // 修改问题编号和标题

// 在此处添加第二个问题的内容

#solution[
在此处添加第二个问题的解析内容。
]

// ==================== 使用说明 ====================
/*
模板使用指南：

1. 文档配置部分：
   - 修改页眉中的"第X周"信息
   - 调整页边距、字体大小等（如需要）

2. 问题添加：
   - 使用 #problem("编号", "标题") 添加新问题
   - 编号格式：如"1-1", "2-3"等
   - 在问题下方添加具体内容

3. 数学公式：
   - 行内公式：$公式内容$
   - 行间公式：$ 公式内容 $（注意空格）
   - 公式会自动居中并添加适当间距

4. 解析框：
   - 使用 #solution[内容] 添加红色解析框
   - 解析内容会自动格式化

5. 文本格式：
   - 粗体：*文本* 或 #text(weight: "bold")[文本]
   - 斜体：_文本_
   - 颜色：#text(fill: rgb("#颜色代码"))[文本]

6. 常用物理常数（可复制使用）：
   - 斯特藩-玻尔兹曼常数：$sigma = 5.67 times 10^(-8) space "W·m"^(-2)·"K"^(-4)$
   - 万有引力常数：$G = 6.67 times 10^(-11) space "N·m"^2·"kg"^(-2)$
   - 光速：$c = 3.00 times 10^8 space "m·s"^(-1)$
   - 普朗克常数：$h = 6.63 times 10^(-34) space "J·s"$

7. 编译命令：
   typst compile 文件名.typ

8. 注意事项：
   - 保持段落首行缩进一致
   - 区分行内公式和行间公式
   - 解析内容要简洁明了
   - 保持学术文档的专业性
*/
